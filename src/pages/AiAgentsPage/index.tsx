import clsx from 'clsx';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { FaPlay } from 'react-icons/fa';
import { IoMdPause } from 'react-icons/io';
import { MdMoreVert } from 'react-icons/md';
import { Link, useLocation } from 'react-router-dom';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { NotificationContainer } from '@/components/ui';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useNotifications } from '@/hooks/useNotifications';
import { cn } from '@/lib/twMerge/cn';

import { Icons } from '../../assets/icons/DashboardIcons';
import { Button } from '../../components/ui';
import AgentSuiteSkeletonLoader from '../../components/ui/AgentSuiteSkeleton';
import { ROUTES } from '../../constants/routes';
import { useAuth } from '../../context/AuthContext';
import {
  agentSuites as mockAgentsSuite,
  marketplaceAgents,
} from '../../data/constants';
import { useGetAIAgentsData } from '../../hooks/useAgents';
import { useHeartbeat } from '../../hooks/useHeartbeat';
import { AIAgent, AIAgentSuite } from '../../types/agents';
import type { HeartbeatStatus } from '../../types/heartbeat';

type TabType = 'Agents Suites' | 'AI Agents';

const AIAgentsPage: React.FC = () => {
  const {
    claimedSuites,
    isUserLoading: isLoadingUserProfile,
    isAuthenticated,
  } = useAuth();
  const { activeAgent, setActiveAgent } = useTenant();
  const location = useLocation();
  const locationState = location.state as {
    selectedAgent?: string;
    userMessage?: string;
  } | null;

  const [activeTab, setActiveTab] = useState<TabType>('Agents Suites');

  // Use React Query hooks for data fetching
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();

  // Initialize notification system
  const { notifications, dismiss } = useNotifications();

  // Heartbeat functionality
  const {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
    getHeartbeatStatus,
  } = useHeartbeat();

  useEffect(() => {
    const switchAgent = async () => {
      if (locationState?.selectedAgent === 'regis') {
        try {
          await setActiveAgent(locationState.selectedAgent);
          // console.log(
          //   `Successfully switched to agent: ${locationState.selectedAgent}`
          // );
        } catch (error) {
          console.error('Failed to switch to regis agent:', error);
        }
      }
    };

    switchAgent();
  }, [locationState?.selectedAgent, setActiveAgent]);

  // Fetch heartbeat data when component mounts - only if user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchHeartbeats();
    }
  }, [fetchHeartbeats, isAuthenticated]);

  // Handle heartbeat actions
  const handleHeartbeatAction = async (
    action: 'initialize' | 'pause',
    agentKey: string
  ): Promise<void> => {
    try {
      if (action === 'initialize') {
        await initializeHeartbeat(agentKey);
      } else {
        await pauseHeartbeat(agentKey);
      }
    } catch (error) {
      // Error handling is done in the useHeartbeat hook
      console.error('Heartbeat action failed:', error);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const isMobile = useMediaQuery('(max-width: 768px)');

  const [isChatDrawerOpen, setIsChatDrawerOpen] = useState(false);
  const [currentSuiteKey, setCurrentSuiteKey] = useState<string | null>(null);
  const [currentAgentKey, setCurrentAgentKey] = useState<string | null>(null);
  const [isAgentSwitcherOpen, setIsAgentSwitcherOpen] = useState(false);

  const currentSuite =
    agentSuites?.find(suite => suite.agentSuiteKey === currentSuiteKey) || null;

  const suiteAgents: AIAgent[] = currentSuite
    ? agents.filter(agent => agent.agentSuiteKey === currentSuite.agentSuiteKey)
    : [];

  const currentAgent: AIAgent | undefined =
    agents.find(agent => agent.agentKey === currentAgentKey) || undefined;

  const agentOptions: DropdownOption[] = suiteAgents.map(agent => ({
    id: agent.agentKey,
    name: agent.agentName,
    icon: agent.avatar,
  }));

  const currentAgentOption: DropdownOption | undefined = currentAgent
    ? {
        id: currentAgent.agentKey,
        name: currentAgent.agentName,
        icon: currentAgent.avatar,
      }
    : undefined;

  const handleAgentSelect = async (agent: AIAgent) => {
    try {
      await setActiveAgent(agent.agentKey);
      // console.log(`Successfully switched to agent: ${agentKey}`);
    } catch (error) {
      console.error('Failed to switch agent:', error);
      return;
    }

    // On mobile, open the full-screen chat drawer for the selected agent
    if (isMobile) {
      setCurrentSuiteKey(agent.agentSuiteKey ?? null);
      setCurrentAgentKey(agent.agentKey);
      setIsChatDrawerOpen(true);
    }
  };

  const handleAgentSwitch = async (option: DropdownOption) => {
    const agent = suiteAgents.find(a => a.agentKey === option.id);
    if (!agent) return;
    await handleAgentSelect(agent);
  };

  // Close drawer automatically when switching to desktop layout
  useEffect(() => {
    if (!isMobile && isChatDrawerOpen) {
      setIsChatDrawerOpen(false);
    }
  }, [isMobile, isChatDrawerOpen]);

  const tabs: TabType[] = ['Agents Suites', 'AI Agents'];

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="flex h-full w-full flex-col p-4 sm:p-8">
        {/* Header */}
        <div className="mb-8 w-full max-w-[606px] ">
          <div className="mb-6 hidden items-center gap-4 sm:flex">
            <Icons.Agent className="h-6 w-6 text-primary" />

            <h1 className="text-blackTree text-xl font-bold">Agents Hub</h1>
          </div>

          {/* Tabs */}
          <div className="flex space-x-2 border-b border-gray-200 sm:space-x-1">
            {tabs.map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={clsx(
                  'px-2 py-2 font-spartan text-sm font-medium transition-colors sm:px-4',
                  activeTab === tab
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-gray-600 hover:text-blackOne'
                )}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div
          className={cn(
            'w-full pb-8',
            activeTab === 'Agents Suites' ? 'max-w-[606px]' : 'max-w-[732px]'
          )}
        >
          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="z-50 mb-8"
            maxNotifications={3}
          />

          {error && (
            <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
              <p>Error loading data: {error.message}</p>
            </div>
          )}

          {activeTab === 'Agents Suites' &&
            (isLoadingSuites ? (
              <AgentSuiteSkeletonLoader count={2} />
            ) : (
              <div className="grid w-full grid-cols-2 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
                {agentSuites.map(suite => {
                  const isMatchingSuite = claimedSuites?.some(
                    claimedSuite =>
                      claimedSuite.suite.agentSuiteKey === suite.agentSuiteKey
                  );
                  return (
                    <AgentSuiteCard
                      key={suite.agentSuiteKey}
                      suite={suite}
                      link={ROUTES.DASHBOARD_AGENT_SUITE(suite.agentSuiteKey)}
                      isSuiteClaimed={isMatchingSuite || false}
                      showClaimButton={!isLoadingUserProfile}
                    />
                  );
                })}
              </div>
            ))}

          {activeTab === 'AI Agents' &&
            (isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={2} />
            ) : (
              <div className="grid w-full flex-1 grid-cols-1 gap-6 md:grid-cols-2">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    agent={agent}
                    isActiveAgent={activeAgent === agent.agentKey}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey
                    )}
                    onAgentSelect={() => handleAgentSelect(agent)}
                    showHeartbeatControl
                    heartbeatStatus={getHeartbeatStatus(agent.agentKey)}
                    onHeartbeatAction={handleHeartbeatAction}
                    isHeartbeatLoading={
                      heartbeatState.loadingAgent === agent.agentKey
                    }
                  />
                ))}
              </div>
            ))}
        </div>
      </div>

      {/* Mobile Chat Drawer */}
      <AnimatePresence>
        {isMobile && isChatDrawerOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex flex-col bg-white md:hidden"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between border-b border-gray-200 p-3">
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => setIsChatDrawerOpen(false)}
                  className="flex items-center justify-center rounded-full text-primary"
                  aria-label="Close chat"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>

                <div className="flex flex-col items-start">
                  <span className="text-sm font-bold uppercase tracking-wide text-blackOne">
                    Agent Suite
                  </span>
                  <span className="hidden text-sm font-semibold text-blackOne">
                    {currentSuite?.agentSuiteName || 'Agent Suite'}
                  </span>
                </div>
              </div>
              <div className="flex items-center">
                {agentOptions.length > 0 && (
                  <AgentsDropdown
                    isOpen={isAgentSwitcherOpen}
                    onToggle={() =>
                      setIsAgentSwitcherOpen(previous => !previous)
                    }
                    currentItem={currentAgentOption}
                    options={agentOptions}
                    onItemSelect={option => {
                      setIsAgentSwitcherOpen(false);
                      void handleAgentSwitch(option);
                    }}
                    placeholder="Select agent"
                    noOptionsMessage="No agents available"
                  />
                )}
              </div>
            </div>

            {/* Chat Content */}
            <div className="flex min-h-0 flex-1 flex-col">
              <EnhancedChatSidebar
                reloadChatHistoryRef={reloadChatHistoryRef}
                className="h-full w-full px-0 py-0 md:hidden"
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </DashboardWithChatLayout>
  );
};

export default AIAgentsPage;

export const AgentCard = ({
  agent,
  showChatButton,
  showHeartbeatControl,
  heartbeatStatus,
  onHeartbeatAction,
  isHeartbeatLoading,
  link,
  customRightComponent,
  className,
  isActiveAgent,
  onAgentSelect,
}: {
  agent: AIAgent;
  link: string;
  showChatButton?: boolean;
  showHeartbeatControl?: boolean;
  heartbeatStatus?: HeartbeatStatus | null;
  onHeartbeatAction?: (
    action: 'initialize' | 'pause',
    agentKey: string
  ) => Promise<void>;
  isHeartbeatLoading?: boolean;
  customRightComponent?: React.ReactNode;
  className?: string;
  isActiveAgent?: boolean;
  onAgentSelect?: () => void;
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const handleClick = (e: React.MouseEvent) => {
    if (onAgentSelect) {
      e.preventDefault(); // Prevent navigation if onAgentSelect is provided
      e.stopPropagation();
      onAgentSelect();
    }
  };

  const handleHeartbeatToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleHeartbeatAction = async (action: 'initialize' | 'pause') => {
    if (onHeartbeatAction && !isHeartbeatLoading) {
      setIsDropdownOpen(false);
      await onHeartbeatAction(action, agent.agentKey);
    }
  };

  // Determine which button to show (priority: heartbeat control > chat button)
  const shouldShowHeartbeatControl = showHeartbeatControl;
  const shouldShowChatButton = showChatButton && !shouldShowHeartbeatControl;

  return (
    <Link
      to={onAgentSelect ? '#' : link} // Use '#' if onAgentSelect is provided to prevent navigation
      onClick={handleClick}
      className={clsx(
        'flex max-w-full items-center overflow-hidden rounded-[10px] border transition-shadow',
        isActiveAgent
          ? 'border-primary bg-[#F5E7DECC]'
          : 'border-transparent bg-[#F5E7DECC] hover:border-primary',
        className,
        onAgentSelect && 'cursor-pointer'
      )}
    >
      {/* Agent Image */}
      {!customRightComponent ? (
        <div className="flex h-full w-[114px] items-center justify-center p-2">
          <div className="h-24 w-20">
            <img
              src={agent.avatar}
              className="h-full w-full object-cover"
              alt={agent.agentName}
              onError={e => {
                // Fallback to mock logo if agent avatar fails to load
                (e.target as HTMLImageElement).src = marketplaceAgents.filter(
                  mockAgent =>
                    mockAgent.name.toLowerCase() ===
                    agent.agentName.toLowerCase()
                )[0].image;
              }}
            />
          </div>
        </div>
      ) : (
        customRightComponent
      )}

      {/* Agent Content */}
      <div className="h-full bg-white p-4">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="font-spartan text-base font-bold leading-[32px] text-blackOne">
            {agent.agentName}
          </h3>

          {/* Heartbeat Control Button (Priority over Chat Button) */}
          {shouldShowHeartbeatControl && (
            <div className="relative flex items-center" ref={dropdownRef}>
              <div>
                {heartbeatStatus === 'ACTIVE' ? (
                  <Icons.HeartbeatActive />
                ) : (
                  <Icons.HeartbeatPaused />
                )}
              </div>
              <button
                onClick={handleHeartbeatToggle}
                disabled={isHeartbeatLoading}
                className={`rounded-xl p-1.5 transition-colors hover:bg-orange-50 ${isHeartbeatLoading ? 'cursor-not-allowed opacity-50' : ''}`}
                aria-label="Heartbeat control menu"
              >
                {isHeartbeatLoading ? (
                  <div className="h-[22px] w-[22px] animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <MdMoreVert size={24} color="#4F4F4F" />
                )}
              </button>

              {/* Heartbeat Dropdown Menu */}
              {isDropdownOpen && !isHeartbeatLoading && (
                <div className="absolute right-0 top-full z-50 mt-1 min-w-[160px] rounded-lg border border-gray-200 bg-white py-1 shadow-lg">
                  <button
                    onClick={e => {
                      handleHeartbeatAction(
                        heartbeatStatus === 'ACTIVE' ? 'pause' : 'initialize'
                      );
                      // e.stopPropagation();
                      // e.preventDefault();
                    }}
                    className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-darkGray hover:bg-gray-50"
                  >
                    {heartbeatStatus === 'ACTIVE' ? (
                      <>
                        <IoMdPause size={15} className="text-darkGray" />
                        Pause Heartbeat
                      </>
                    ) : (
                      <>
                        <FaPlay size={14} className="text-darkGray" />
                        Start Heartbeat
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Chat Button (Only shown if heartbeat control is not shown) */}
          {shouldShowChatButton && (
            <button
              className={`rounded-xl border border-primary p-1.5 ${isActiveAgent ? 'bg-primary' : 'bg-[#FFECE3] hover:bg-orange-50'}`}
            >
              <svg
                width="22"
                height="22"
                viewBox="0 0 22 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.25047 9.62536C8.25047 9.44303 8.32291 9.26816 8.45184 9.13923C8.58077 9.01029 8.75564 8.93786 8.93797 8.93786H13.063C13.2453 8.93786 13.4202 9.01029 13.5491 9.13923C13.678 9.26816 13.7505 9.44303 13.7505 9.62536C13.7505 9.8077 13.678 9.98257 13.5491 10.1115C13.4202 10.2404 13.2453 10.3129 13.063 10.3129H8.93797C8.75564 10.3129 8.58077 10.2404 8.45184 10.1115C8.32291 9.98257 8.25047 9.8077 8.25047 9.62536ZM8.93797 11.6879C8.75564 11.6879 8.58077 11.7603 8.45184 11.8892C8.32291 12.0182 8.25047 12.193 8.25047 12.3754C8.25047 12.5577 8.32291 12.7326 8.45184 12.8615C8.58077 12.9904 8.75564 13.0629 8.93797 13.0629H11.688C11.8703 13.0629 12.0452 12.9904 12.1741 12.8615C12.303 12.7326 12.3755 12.5577 12.3755 12.3754C12.3755 12.193 12.303 12.0182 12.1741 11.8892C12.0452 11.7603 11.8703 11.6879 11.688 11.6879H8.93797ZM2.75047 11.0004C2.75083 9.18405 3.35056 7.41862 4.45664 5.97794C5.56272 4.53725 7.11333 3.50185 8.86791 3.03235C10.6225 2.56284 12.483 2.68548 14.1607 3.38124C15.8385 4.077 17.2398 5.30699 18.1472 6.8804C19.0546 8.45381 19.4174 10.2827 19.1793 12.0833C18.9412 13.884 18.1156 15.5557 16.8305 16.8392C15.5453 18.1228 13.8725 18.9463 12.0716 19.1821C10.2707 19.4179 8.44225 19.0529 6.86997 18.1435L3.65522 19.216C3.53646 19.2556 3.40911 19.2621 3.28694 19.2347C3.16476 19.2073 3.0524 19.147 2.96197 19.0604C2.87154 18.9738 2.80648 18.8641 2.77381 18.7432C2.74114 18.6224 2.74211 18.4949 2.7766 18.3745L3.7556 14.9494C3.09563 13.7378 2.75004 12.38 2.75047 11.0004ZM11.0005 4.12536C9.7866 4.12529 8.59434 4.4466 7.5449 5.05666C6.49546 5.66671 5.62625 6.54375 5.02562 7.59861C4.42499 8.65348 4.11437 9.84857 4.12532 11.0624C4.13627 12.2762 4.46841 13.4655 5.08797 14.5094C5.13564 14.59 5.16633 14.6795 5.17815 14.7724C5.18997 14.8654 5.18268 14.9597 5.15672 15.0497L4.45685 17.4972L6.7366 16.7382C6.83342 16.7059 6.93622 16.6956 7.03754 16.7079C7.13885 16.7203 7.23614 16.7551 7.32235 16.8097C8.22335 17.3799 9.24462 17.7328 10.3055 17.8403C11.3663 17.9478 12.4376 17.8072 13.4347 17.4294C14.4318 17.0516 15.3273 16.4471 16.0506 15.6636C16.7738 14.8801 17.3049 13.9391 17.6019 12.9151C17.8988 11.891 17.9535 10.8119 17.7616 9.763C17.5697 8.71414 17.1365 7.7243 16.4961 6.87171C15.8558 6.01912 15.0259 5.32716 14.0721 4.85051C13.1183 4.37387 12.0667 4.12561 11.0005 4.12536Z"
                  fill={isActiveAgent ? '#FFFFFF' : '#121212'}
                />
              </svg>
            </button>
          )}
        </div>
        <h4 className="mb-2 font-inter text-sm font-semibold text-subText">
          {agent.description}
        </h4>
        <p className="font-inter text-sm text-subText">
          {agent.roleDescription}
        </p>
      </div>
    </Link>
  );
};

export const AgentSuiteCard = ({
  suite,
  link,
  onAgentSuiteClick,
  isSuiteClaimed,
  showClaimButton = true,
}: {
  suite: AIAgentSuite;
  link: string;
  onAgentSuiteClick?: () => void;
  isSuiteClaimed: boolean;
  showClaimButton?: boolean;
}) => (
  <Link
    to={onAgentSuiteClick ? '#' : link}
    state={suite}
    className="group block"
    onClick={onAgentSuiteClick}
  >
    <div className="h-full max-w-[290px] overflow-hidden rounded-xl border border-gray-200 bg-white transition-shadow hover:border-primary sm:rounded-2xl">
      {/* Card Image */}
      <img
        src={suite.avatar}
        className="h-[120px] w-full object-cover md:h-[200px]"
        alt={suite.agentSuiteName}
        onError={e => {
          // Fallback to mock logo if agent avatar fails to load
          (e.target as HTMLImageElement).src = mockAgentsSuite.filter(
            agent =>
              agent.id.toLowerCase() === suite.agentSuiteKey.toLowerCase()
          )[0].image;
        }}
      />

      {/* Card Content */}
      <div className="flex flex-col gap-3 p-4 font-inter text-blackOne">
        <div className="flex w-fit items-center justify-center rounded border border-grayNine bg-grayNineTeen px-2 py-[2px]">
          <span className="text-sm font-semibold">{suite?.agentSuiteName}</span>
        </div>
        <p className="whitespace-pre-line text-[13px] font-semibold leading-[18px] sm:text-base sm:leading-[22px]">
          {suite?.description}
        </p>
        <p className="whitespace-pre-line text-[13px] leading-[18px] text-subText sm:text-sm sm:leading-[21px]">
          {suite?.roleDescription}
        </p>
        {showClaimButton &&
          (isSuiteClaimed ? null : (
            <Button
              className="h-10 w-fit font-spartan font-normal capitalize text-white"
              onClick={onAgentSuiteClick}
            >
              claim suite
            </Button>
          ))}
      </div>
    </div>
  </Link>
);
