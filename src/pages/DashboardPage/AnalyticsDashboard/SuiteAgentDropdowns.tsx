import React, { useEffect, useMemo, useRef, useState } from 'react';

import { useAuth } from '@/context/AuthContext';
import { useTenant } from '@/context/TenantContext';
import { marketplaceAgents } from '@/data/constants';
import { useGetAIAgents, useGetAIAgentSuites } from '@/hooks/useAgents';
import { useHeartbeat } from '@/hooks/useHeartbeat';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { UserBasicInfoPayload } from '@/types/user';
import { useAnalyticsParams } from '@/utils/urlParams';

import AgentsDropdown, {
  DropdownOption,
} from '../../../components/ui/AgentsDropdown';

interface SuiteAgentDropdownProps {
  className?: string;
}

const SuiteAgentDropdown: React.FC<SuiteAgentDropdownProps> = ({
  className,
}) => {
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { filters, updateFilters } = useAnalyticsParams();
  const { setActiveAgent } = useTenant();
  const { isAuthenticated } = useAuth();
  const { data: allAgentSuites = [] } = useGetAIAgentSuites();
  const { data: allAgents = [] } = useGetAIAgents();
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] =
    useState<boolean>(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] =
    useState<boolean>(false);

  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  // Heartbeat functionality
  const { fetchHeartbeats, getHeartbeatStatus } = useHeartbeat();

  // Fetch heartbeat data when component mounts - only if user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchHeartbeats();
    }
  }, [fetchHeartbeats, isAuthenticated]);

  const suiteOptions = useMemo<DropdownOption[]>(() => {
    const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites || [];

    // If user has claimed suites, use them
    if (claimedSuites.length > 0) {
      return claimedSuites.map(suite => ({
        id: suite.suite.agentSuiteKey,
        name: suite.suite.agentSuiteName,
        icon: suite.suite.avatar,
      }));
    }

    // Fallback to all available agent suites from API
    return allAgentSuites.map(suite => ({
      id: suite.agentSuiteKey,
      name: suite.agentSuiteName,
      icon: suite.avatar,
    }));
  }, [userData, allAgentSuites]);

  const currentSuite = useMemo(
    () => suiteOptions.find(s => s.id === filters.suite) || suiteOptions[0],
    [filters.suite, suiteOptions]
  );

  const agentOptions = useMemo<DropdownOption[]>(() => {
    if (!filters.suite) return [];

    let agents: DropdownOption[] = [];

    // First try to get agents from user's tenant data
    if (userData?.tenantAgents) {
      const tenantAgents = userData.tenantAgents;
      const suiteAgents = tenantAgents.filter(
        agent => agent.agentSuiteKey === filters.suite
      );

      if (suiteAgents.length > 0) {
        agents = suiteAgents.map(agent => ({
          id: agent.agentKey,
          name: agent.agentName,
          icon: agent.avatar,
          heartbeatStatus: getHeartbeatStatus(agent.agentKey),
        }));
      }
    }

    // Fallback to all agents from API filtered by suite
    if (agents.length === 0) {
      const suiteAgents = allAgents.filter(
        agent => agent.agentKey === filters.suite
      );

      agents = suiteAgents.map(agent => ({
        id: agent.agentKey,
        name: agent.agentName,
        icon: agent.avatar,
        heartbeatStatus: getHeartbeatStatus(agent.agentKey),
      }));
    }

    // Add "All" option at the top with suite icon
    return [
      // {
      //   id: 'all',
      //   name: 'All',
      //   icon: currentSuite?.icon || '',
      // },
      ...agents,
    ];
  }, [filters.suite, userData, allAgents, getHeartbeatStatus]);

  const currentAgent = useMemo(
    () => agentOptions.find(a => a.id === filters.agent) || agentOptions[0],
    [filters.agent, agentOptions]
  );

  const handleSuiteChange = (suite: DropdownOption) => {
    // Get agents for the new suite
    let newSuiteAgents: DropdownOption[] = [];

    // First try to get agents from user's tenant data
    if (userData?.tenantAgents) {
      const suiteAgents = userData.tenantAgents.filter(
        agent => agent.agentSuiteKey === suite.id
      );

      if (suiteAgents.length > 0) {
        newSuiteAgents = suiteAgents.map(agent => ({
          id: agent.agentKey,
          name: agent.agentName,
          icon: agent.avatar,
        }));
      }
    }

    // Fallback to all agents from API filtered by suite
    if (newSuiteAgents.length === 0) {
      const suiteAgents = allAgents.filter(
        agent => agent.agentSuiteKey === suite.id
      );

      newSuiteAgents = suiteAgents.map(agent => ({
        id: agent.agentKey,
        name: agent.agentName,
        icon: agent.avatar,
      }));
    }

    // Set the first agent of the new suite
    const firstAgent = newSuiteAgents[0];
    if (firstAgent) {
      updateFilters({ suite: suite.id, agent: firstAgent.id });
      setActiveAgent(firstAgent.id);
    } else {
      updateFilters({ suite: suite.id, agent: undefined });
      setActiveAgent(suite.id);
    }

    setIsSuiteDropdownOpen(false);
  };

  const handleAgentChange = (agent: DropdownOption) => {
    updateFilters({ agent: agent.id });
    setIsAgentDropdownOpen(false);
    setActiveAgent(agent.id);
  };

  useEffect(() => {
    if (suiteOptions.length > 0 && !filters.suite) {
      updateFilters({ suite: suiteOptions[0].id });
    }
    if (agentOptions.length > 0 && !filters.agent) {
      updateFilters({ agent: agentOptions[0].id });
    }
  }, [suiteOptions, agentOptions, filters, updateFilters]);

  const handleAgentError = (
    event: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    (event.target as HTMLImageElement).src =
      marketplaceAgents.find(
        mockAgent =>
          mockAgent.name.toLowerCase() === currentAgent?.name.toLowerCase()
      )?.image || '';
  };

  const getAgentOptionError =
    (option: DropdownOption) =>
    (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
      (event.target as HTMLImageElement).src =
        marketplaceAgents.find(
          mockAgent =>
            mockAgent.name.toLowerCase() === option.name.toLowerCase()
        )?.image || '';
    };

  return (
    <div
      className={`flex flex-wrap items-center gap-3 sm:flex-nowrap sm:gap-8 ${className}`}
    >
      <div className="relative" ref={suiteDropdownRef}>
        <AgentsDropdown
          isOpen={isSuiteDropdownOpen}
          onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
          currentItem={currentSuite}
          options={suiteOptions}
          onItemSelect={handleSuiteChange}
          placeholder="Suite"
          noOptionsMessage="No suites"
        />
      </div>

      <div className="relative" ref={agentDropdownRef}>
        <AgentsDropdown
          isOpen={isAgentDropdownOpen}
          onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
          currentItem={currentAgent}
          options={agentOptions}
          onItemSelect={handleAgentChange}
          placeholder="Agent"
          noOptionsMessage="No agents"
          onImageError={handleAgentError}
          getOptionImageError={getAgentOptionError}
          showHeartbeat={true}
        />
      </div>
    </div>
  );
};

export default SuiteAgentDropdown;
