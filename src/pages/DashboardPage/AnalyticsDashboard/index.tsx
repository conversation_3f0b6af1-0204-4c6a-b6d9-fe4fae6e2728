import { AnimatePresence, motion } from 'framer-motion';
import { Plus, Search, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Input, NotificationContainer } from '@/components/ui';
import AnimatedTabs, { Tab } from '@/components/ui/AnimatedTabs';
import DateRangePicker from '@/components/ui/DateRangePicker';
import FilterPanel from '@/components/ui/FilterPanel';
import { ROUTES } from '@/constants/routes';
import { useAuth } from '@/context/AuthContext';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useNotifications } from '@/hooks/useNotifications';

import SuiteAgentDropdown from './SuiteAgentDropdowns';

const parseDateFromParam = (dateStr: string): Date => {
  const [year, month, day] = dateStr.split('-').map(Number);
  return new Date(year, month - 1, day);
};

const AnalyticsDashboard: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { claimedSuites } = useAuth();
  const isMobile = useMediaQuery('(max-width: 768px)');

  const { notifications, dismiss } = useNotifications();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const filterButtonRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLDivElement>(null);

  // ALL STATE HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL LOGIC
  // Date range picker state
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const [currentDateRangeType, setCurrentDateRangeType] = useState<string>('');
  const [showFilterOptions, setShowFilterOptions] = useState(false);
  const [showMobileControls, setShowMobileControls] = useState(false);

  // Get search params from URL
  const currentSearchParams = new URLSearchParams(location.search);
  const currentSearchTerm = currentSearchParams.get('search') || '';

  useEffect(() => {
    if (!isMobile) {
      setShowMobileControls(false);
    }
  }, [isMobile]);

  const toggleMobileControls = () => {
    setShowMobileControls(prev => {
      const next = !prev;
      if (!next) {
        setShowFilterOptions(false);
      }
      return next;
    });
  };

  // Check if user has at least one claimed suite, if not redirect to dashboard welcome
  useEffect(() => {
    if (
      claimedSuites !== null &&
      (!claimedSuites || claimedSuites.length === 0)
    ) {
      navigate(ROUTES.DASHBOARD_BASE, { replace: true });
    }
  }, [claimedSuites, navigate]);

  // Show loading if we're still checking claimed suites
  if (claimedSuites === null) {
    return (
      <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
        <div className="flex h-64 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </DashboardWithChatLayout>
    );
  }

  // If no claimed suites, component will be redirected in useEffect above
  if (!claimedSuites || claimedSuites.length === 0) {
    return null;
  }

  const getSelectedFilters = () => {
    const filters = [];

    // Only show filters relevant to the current tab
    if (activeTab === 'insights') {
      if (currentSearchParams.get('from') || currentSearchParams.get('to'))
        filters.push('date');
    } else if (activeTab === 'task-logs') {
      if (currentSearchParams.get('status')) filters.push('status');
      if (currentSearchParams.get('priority')) filters.push('priority');
      if (currentSearchParams.get('from') || currentSearchParams.get('to'))
        filters.push('date');
    } else if (activeTab === 'assignment-logs') {
      if (
        currentSearchParams.get('assignedDateFrom') ||
        currentSearchParams.get('assignedDateTo')
      )
        filters.push('assignment-date');
      if (
        currentSearchParams.get('startDateFrom') ||
        currentSearchParams.get('startDateTo')
      )
        filters.push('start-date');
      if (currentSearchParams.get('status')) filters.push('status');
    }

    return filters;
  };

  const handleSearchChange = (value: string) => {
    // Clear existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('search', value);

    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;
    navigate(newUrl, { replace: true });
  };

  const tabs: Tab[] = [
    {
      id: 'task-logs',
      label: 'Task Logs',
    },
    {
      id: 'insights',
      label: 'Insights',
    },
    // {
    //   id: 'assignment-logs',
    //   label: 'Assignment Logs',
    // },
  ];

  // Determine active tab based on current path
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/insights')) return 'insights';
    if (path.includes('/assignment-logs')) return 'assignment-logs';
    return 'task-logs'; // Default to task-logs
  };

  const activeTab = getActiveTabFromPath();

  // Priority options for task logs
  const priorityOptions = [
    { id: 'low', label: 'Low', value: 'LOW' },
    { id: 'medium', label: 'Medium', value: 'MEDIUM' },
    { id: 'high', label: 'High', value: 'HIGH' },
  ];

  // Status options for task logs and assignment logs
  const statusOptions = [
    { id: 'queued', label: 'Queued', value: 'QUEUED' },
    { id: 'not-started', label: 'Not Started', value: 'NOT_STARTED' },
    { id: 'in-progress', label: 'In Progress', value: 'IN_PROGRESS' },
    { id: 'completed', label: 'Completed', value: 'COMPLETED' },
    { id: 'escalated', label: 'Escalated', value: 'ESCALATED' },
    { id: 'failed', label: 'Failed', value: 'FAILED' },
  ];

  const handlePrioritySelection = (selectedValues: string[]) => {
    const newSearchParams = new URLSearchParams(location.search);

    if (selectedValues.length > 0) {
      newSearchParams.set('priority', selectedValues.join(','));
    } else {
      newSearchParams.delete('priority');
    }

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  const handleStatusSelection = (selectedValues: string[]) => {
    const newSearchParams = new URLSearchParams(location.search);

    if (selectedValues.length > 0) {
      newSearchParams.set('status', selectedValues.join(','));
    } else {
      newSearchParams.delete('status');
    }

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  // Get current status/priority selections for display
  const getCurrentStatusSelections = () => {
    const statusParam = currentSearchParams.get('status');
    return statusParam ? statusParam.split(',') : [];
  };

  const getCurrentPrioritySelections = () => {
    const priorityParam = currentSearchParams.get('priority');
    return priorityParam ? priorityParam.split(',') : [];
  };

  // Clear all filters function
  const clearAllFilters = () => {
    const newSearchParams = new URLSearchParams();

    // Keep only suite and agent parameters
    const suite = currentSearchParams.get('suite');
    const agent = currentSearchParams.get('agent');

    if (suite) newSearchParams.set('suite', suite);
    if (agent) newSearchParams.set('agent', agent);

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });

    // Reset date picker state
    setShowDateRangePicker(false);
    setCurrentDateRangeType('');
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    const filters = getSelectedFilters();
    return filters.length > 0;
  };

  const formatDateForBackend = (date: Date): string => {
    // Format as yyyy-MM-dd (LocalDate format)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleDateRangeApply = (range: { from?: Date; to?: Date }) => {
    const newSearchParams = new URLSearchParams(location.search);

    if (currentDateRangeType === 'date') {
      // Task logs and insights date range
      if (range.from) {
        newSearchParams.set('from', formatDateForBackend(range.from));
      } else {
        newSearchParams.delete('from');
      }
      if (range.to) {
        newSearchParams.set('to', formatDateForBackend(range.to));
      } else {
        newSearchParams.delete('to');
      }
    } else if (currentDateRangeType === 'assignment-date') {
      // Assignment date range
      if (range.from) {
        newSearchParams.set(
          'assignedDateFrom',
          formatDateForBackend(range.from)
        );
      } else {
        newSearchParams.delete('assignedDateFrom');
      }
      if (range.to) {
        newSearchParams.set('assignedDateTo', formatDateForBackend(range.to));
      } else {
        newSearchParams.delete('assignedDateTo');
      }
    } else if (currentDateRangeType === 'start-date') {
      // Start date range
      if (range.from) {
        newSearchParams.set('startDateFrom', formatDateForBackend(range.from));
      } else {
        newSearchParams.delete('startDateFrom');
      }
      if (range.to) {
        newSearchParams.set('startDateTo', formatDateForBackend(range.to));
      } else {
        newSearchParams.delete('startDateTo');
      }
    }

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
    setShowDateRangePicker(false);
  };

  const getCurrentDateRange = () => {
    let from: Date | undefined, to: Date | undefined;

    if (currentDateRangeType === 'date') {
      from = currentSearchParams.get('from')
        ? parseDateFromParam(currentSearchParams.get('from')!)
        : undefined;
      to = currentSearchParams.get('to')
        ? parseDateFromParam(currentSearchParams.get('to')!)
        : undefined;
    } else if (currentDateRangeType === 'assignment-date') {
      from = currentSearchParams.get('assignedDateFrom')
        ? parseDateFromParam(currentSearchParams.get('assignedDateFrom')!)
        : undefined;
      to = currentSearchParams.get('assignedDateTo')
        ? parseDateFromParam(currentSearchParams.get('assignedDateTo')!)
        : undefined;
    } else if (currentDateRangeType === 'start-date') {
      from = currentSearchParams.get('startDateFrom')
        ? parseDateFromParam(currentSearchParams.get('startDateFrom')!)
        : undefined;
      to = currentSearchParams.get('startDateTo')
        ? parseDateFromParam(currentSearchParams.get('startDateTo')!)
        : undefined;
    }

    return { from, to };
  };

  // Check if we're on a child route (detail page)
  const isChildRoute =
    location.pathname.includes('/all') ||
    (location.pathname !== ROUTES.DASHBOARD_ANALYTICS_INSIGHTS &&
      location.pathname !== ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS &&
      location.pathname !== ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS);

  const handleTabChange = (tabId: string) => {
    // Clear page-specific filters when switching tabs, but keep suite/agent
    const newSearchParams = new URLSearchParams();

    // Keep only suite and agent parameters from current URL
    const currentParams = new URLSearchParams(location.search);
    const suite = currentParams.get('suite');
    const agent = currentParams.get('agent');

    if (suite) newSearchParams.set('suite', suite);
    if (agent) newSearchParams.set('agent', agent);

    switch (tabId) {
      case 'insights':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}?${newSearchParams.toString()}`
        );
        break;
      case 'task-logs':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS}?${newSearchParams.toString()}`
        );
        break;
      case 'assignment-logs':
        navigate(
          `${ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS}?${newSearchParams.toString()}`
        );
        break;
    }
  };

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      {/* Conditionally render header and tabs only on parent pages */}
      {!isChildRoute && (
        <div className="space-y-4 p-4 sm:p-6">
          <div
            className={
              isMobile
                ? 'space-y-3'
                : 'flex w-full items-center justify-between'
            }
          >
            {isMobile ? (
              <>
                <div className="flex w-full items-center gap-2">
                  <SuiteAgentDropdown className="flex-1" />
                  <motion.button
                    type="button"
                    onClick={toggleMobileControls}
                    className="flex h-9 w-9 items-center justify-center rounded-lg border border-[#FFE0D1] bg-[#FFF1EB] text-primary focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
                    whileTap={{ scale: 0.95 }}
                    aria-label={
                      showMobileControls
                        ? 'Hide search and filters'
                        : 'Show search and filters'
                    }
                  >
                    <motion.div
                      animate={{ rotate: showMobileControls ? 45 : 0 }}
                    >
                      <Plus className="h-4 w-4" strokeWidth={2} />
                    </motion.div>
                  </motion.button>
                </div>

                <AnimatePresence initial={false}>
                  {showMobileControls && (
                    <motion.div
                      key="mobile-controls"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                      className="flex w-full flex-row items-center gap-2"
                    >
                      <Input
                        type="text"
                        placeholder="Search"
                        value={currentSearchTerm}
                        onChange={e => handleSearchChange(e.target.value)}
                        className="h-9 w-full flex-1 rounded-lg border border-[#D9D9D9] bg-white pl-11 pr-4 text-sm text-[#2F2F2F] placeholder:text-xs placeholder:text-[#979797] focus:outline-none focus:ring-2 focus:ring-primary/20"
                        startIcon={
                          <Search className="h-4 w-4 text-[#979797]" />
                        }
                        endIcon={
                          currentSearchTerm ? (
                            <button
                              onClick={() => handleSearchChange('')}
                              className="flex h-5 w-5 items-center justify-center rounded-full bg-transparent text-primary"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          ) : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() => setShowFilterOptions(!showFilterOptions)}
                        className="flex h-9 w-28 flex-1 items-center justify-center gap-2 rounded-lg border border-[#FFE0D1] bg-[#FFF1EB] p-2 text-sm text-primary transition-colors hover:bg-primary hover:text-white"
                      >
                        <span>Filter</span>
                        <Icons.DownloadCloud className="h-4 w-4" />
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </>
            ) : (
              <>
                {/* Header with Dropdowns */}
                <SuiteAgentDropdown />

                {/* Search and Filter */}
                <div className="flex items-center justify-end gap-3">
                  <Input
                    type="text"
                    placeholder="Search"
                    value={currentSearchTerm}
                    onChange={e => handleSearchChange(e.target.value)}
                    className="h-[44px] w-[215px] rounded-[10px] border border-[#D9D9D9] p-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2 focus:ring-primary"
                    startIcon={<Search className="h-5 w-5 text-[#979797]" />}
                    endIcon={
                      currentSearchTerm && (
                        <button
                          onClick={() => handleSearchChange('')}
                          className="flex h-5 w-5 items-center justify-center bg-transparent text-primary hover:bg-transparent"
                        >
                          <X className="h-5 w-5" />
                        </button>
                      )
                    }
                  />
                  <div ref={filterButtonRef}>
                    <button
                      onClick={() => setShowFilterOptions(!showFilterOptions)}
                      className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] px-4 text-primary transition-colors hover:bg-primary hover:text-white"
                    >
                      <span className="text-sm font-medium">Filter</span>
                      <Icons.DownloadCloud className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
          {/* Filter Options */}
          <AnimatePresence>
            {showFilterOptions && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="w-full"
              >
                <FilterPanel
                  activeTab={activeTab}
                  onDateFilterClick={() => {
                    setCurrentDateRangeType('date');
                    setShowDateRangePicker(true);
                  }}
                  onPrioritySelection={handlePrioritySelection}
                  onStatusSelection={handleStatusSelection}
                  currentSearchParams={currentSearchParams}
                  dateButtonRef={dateButtonRef}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Active Filters Display */}
          <AnimatePresence>
            {hasActiveFilters() && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="flex flex-wrap items-center gap-2 sm:flex-nowrap sm:gap-3"
              >
                <button
                  onClick={clearAllFilters}
                  className="flex h-[36px] items-center gap-2 rounded-lg bg-grayTen px-4 py-2 text-sm text-white transition-colors hover:bg-gray-900"
                >
                  <span>Clear</span>
                  <X className="h-5 w-5" />
                </button>

                {/* Priority Filter Chip */}
                {getCurrentPrioritySelections().length > 0 && (
                  <div className="flex items-center gap-2 rounded-lg border border-grayTen bg-[#FFFAF7] px-3 py-2">
                    <Icons.OutlineTransaction className="h-4 w-4" />
                    <span className="text-sm">Priority</span>
                    <button
                      onClick={() => handlePrioritySelection([])}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                )}

                {/* Individual Priority Chips */}
                {getCurrentPrioritySelections().map(priority => (
                  <div
                    key={priority}
                    className="flex items-center gap-2 rounded-lg border border-grayTen bg-[#FFFAF7] px-3 py-2"
                  >
                    <span className="text-sm">
                      {priorityOptions.find(p => p.value === priority)?.label}
                    </span>
                    <button
                      onClick={() => {
                        const newSelections =
                          getCurrentPrioritySelections().filter(
                            p => p !== priority
                          );
                        handlePrioritySelection(newSelections);
                      }}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Status Filter Chip */}
                {getCurrentStatusSelections().length > 0 && (
                  <div className="flex items-center gap-2 rounded-lg border border-grayTen bg-[#FFFAF7] px-3 py-2">
                    <Icons.StatusFilled className="h-4 w-4" />
                    <span className="text-sm">Status</span>
                    <button
                      onClick={() => handleStatusSelection([])}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                )}

                {/* Individual Status Chips */}
                {getCurrentStatusSelections().map(status => (
                  <div
                    key={status}
                    className="flex items-center gap-2 rounded-lg border border-grayTen bg-[#FFFAF7] px-3 py-2"
                  >
                    <span className="text-sm">
                      {statusOptions.find(s => s.value === status)?.label}
                    </span>
                    <button
                      onClick={() => {
                        const newSelections =
                          getCurrentStatusSelections().filter(
                            s => s !== status
                          );
                        handleStatusSelection(newSelections);
                      }}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Date Filter Chip */}
                {(currentSearchParams.get('from') ||
                  currentSearchParams.get('to')) && (
                  <div className="flex items-center gap-2 rounded-lg border border-grayTen bg-[#FFFAF7] px-3 py-2">
                    <Icons.DataDate className="h-4 w-4" />
                    <button
                      onClick={() => {
                        setCurrentDateRangeType('date');
                        setShowDateRangePicker(true);
                      }}
                      className="text-sm hover:text-primary"
                    >
                      {currentSearchParams.get('from')
                        ? parseDateFromParam(currentSearchParams.get('from')!)
                            .toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric',
                            })
                            .replace(/\//g, '.')
                        : ''}
                      {currentSearchParams.get('from') &&
                        currentSearchParams.get('to') &&
                        ' → '}
                      {currentSearchParams.get('to')
                        ? parseDateFromParam(currentSearchParams.get('to')!)
                            .toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric',
                            })
                            .replace(/\//g, '.')
                        : ''}
                    </button>
                    <button
                      onClick={() => {
                        const newSearchParams = new URLSearchParams(
                          location.search
                        );
                        newSearchParams.delete('from');
                        newSearchParams.delete('to');
                        navigate(
                          `${location.pathname}?${newSearchParams.toString()}`,
                          {
                            replace: true,
                          }
                        );
                        setShowDateRangePicker(false);
                        setCurrentDateRangeType('');
                      }}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="z-50 mb-8"
            maxNotifications={3}
          />

          {/* Tabs */}
          <AnimatedTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            showContent={false}
          />
        </div>
      )}

      {/* Content */}
      <div className="flex-1 pb-10">
        <Outlet />
      </div>

      {/* Date Range Picker Popup */}
      <DateRangePicker
        key={`${currentSearchParams.get('from')}-${currentSearchParams.get('to')}-${currentDateRangeType}`}
        isOpen={showDateRangePicker}
        onClose={() => setShowDateRangePicker(false)}
        onApply={handleDateRangeApply}
        initialRange={getCurrentDateRange()}
        anchorRef={dateButtonRef}
      />
    </DashboardWithChatLayout>
  );
};

export default AnalyticsDashboard;
