import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';
import { useNotifications } from '@/hooks/useNotifications';
import {
  useDeleteTaskLogMutation,
  useTaskLogDetails,
  useUpdateTaskLogMutation,
} from '@/hooks/useTaskLog';
import {
  mapDisplayToPriority,
  mapDisplayToStatus,
  mapPriorityToDisplay,
  mapStatusToDisplay,
  TaskLogDetailsFormData,
  TaskLogResponse,
} from '@/types/taskLog';

import {
  DeleteTaskLogModal,
  LogDetailsError,
  LogDetailsHeader,
  LogDetailsSkeleton,
  SuccessModal,
  TaskLogAdditionalFields,
  TaskLogBasicInfo,
  TaskLogStatusPriority,
} from './components';

const TaskLogDetailsPage: React.FC = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const { tenantId } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const { notifyCustom } = useNotifications();
  const taskLogId = taskId;
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editForm, setEditForm] = useState<Partial<TaskLogDetailsFormData>>({});
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Use the new hook to fetch task log details
  const {
    data: taskLog,
    isLoading,
    error,
    refetch,
  } = useTaskLogDetails(taskLogId || '', !!taskLogId);

  const updateTaskLogMutation = useUpdateTaskLogMutation();
  const deleteTaskLogMutation = useDeleteTaskLogMutation();

  // Convert TaskLog to TaskLogDetailsFormData for UI
  const convertToFormData = (
    data: TaskLogResponse | undefined
  ): TaskLogDetailsFormData => {
    if (!data) return {} as TaskLogDetailsFormData;

    return {
      taskTitle: data?.data?.taskTitle || '',
      description: data?.data?.successCriteria || 'No description provided',
      time: formatUserTimestamp(data?.data?.createdAt, 'time'),
      dateCreated: data?.data?.createdAt,
      dueDate: data?.data?.dueDate || '',
      createdBy: data.data.assignedTo || 'Unassigned',
      type: data?.data?.taskType || 'General Task',
      priority: mapPriorityToDisplay(data?.data?.priority),
      status: mapStatusToDisplay(data?.data?.status),
      escalationPolicy: data.data.escalationPolicy || 'Default Policy',
    };
  };

  // Use dummy data for testing, fallback to real data
  const formData = convertToFormData(taskLog);

  // Utility function to extract user-friendly error message from API response
  const extractErrorMessage = (error: any): string => {
    // Check for API error response structure
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    // Check for API error details
    if (error?.response?.data?.data?.message) {
      return error.response.data.data.message;
    }

    // Check for direct error message
    if (
      error?.message &&
      !error.message.includes('403') &&
      !error.message.includes('404') &&
      !error.message.includes('500')
    ) {
      return error.message;
    }

    // Fallback user-friendly messages based on status codes
    if (error?.response?.status === 403) {
      return "You don't have permission to perform this action";
    }

    if (error?.response?.status === 404) {
      return 'Task log not found';
    }

    if (error?.response?.status === 500) {
      return 'Something went wrong on our end. Please try again';
    }

    return 'An unexpected error occurred. Please try again';
  };

  // Utility functions for handling messages
  const showError = (message: string) => {
    notifyCustom({
      message,
      type: 'error',
    });
  };

  const showSuccess = (message: string) => {
    notifyCustom({
      message,
      type: 'success',
    });
  };

  const handleBack = () => {
    navigate(-1);
  };

  // Check if form has changes compared to original data
  const checkForChanges = (newFormData: Partial<TaskLogDetailsFormData>) => {
    const hasFormChanges =
      newFormData.description !== formData.description ||
      newFormData.status !== formData.status ||
      newFormData.priority !== formData.priority ||
      newFormData.dueDate !== formData.dueDate ||
      newFormData.escalationPolicy !== formData.escalationPolicy;

    setHasChanges(hasFormChanges);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm(formData);
    setHasChanges(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm(formData);
    setHasChanges(false);
  };

  const handleSave = async () => {
    if (!taskLog || !taskLogId) return;

    try {
      const updateData = {
        id: taskLogId,
        tenantId: tenantId || '',
        status: editForm.status
          ? mapDisplayToStatus(editForm.status)
          : undefined,
        priority: editForm.priority
          ? mapDisplayToPriority(editForm.priority)
          : undefined,
        dueDate: editForm.dueDate
          ? new Date(editForm.dueDate).toISOString()
          : undefined,
        escalationPolicy: editForm.escalationPolicy,
        successCriteria: editForm.description,
        auditEvent: 'TASK_UPDATED',
        auditNotes: 'Task updated via web interface',
      };

      await updateTaskLogMutation.mutateAsync(updateData);
      setIsEditing(false);
      setHasChanges(false);
      showSuccess('Task log updated successfully!');
    } catch (error) {
      const errorMsg = extractErrorMessage(error);
      showError(errorMsg);
    }
  };

  const handleInputChange = (
    field: keyof TaskLogDetailsFormData,
    value: string
  ) => {
    const newFormData = {
      ...editForm,
      [field]: value,
    };
    setEditForm(newFormData);
    checkForChanges(newFormData);
  };

  const handleSelectChange = (
    field: keyof TaskLogDetailsFormData,
    value: string
  ) => {
    const newFormData = {
      ...editForm,
      [field]: value,
    };
    setEditForm(newFormData);
    checkForChanges(newFormData);
  };

  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!taskLogId) return;

    try {
      await deleteTaskLogMutation.mutateAsync(taskLogId);
      setShowDeleteModal(false);

      // Show success modal and redirect immediately without error message
      setShowSuccessModal(true);
      setTimeout(() => {
        navigate(-1); // Go back to list page
      }, 1500);
    } catch (error) {
      const errorMsg = extractErrorMessage(error);
      setShowDeleteModal(false);
      showError(errorMsg);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
  };

  // Render main content based on state
  const renderMainContent = () => {
    if (isLoading && !taskLog) {
      return <LogDetailsSkeleton />;
    }

    if (error && !taskLog) {
      return (
        <LogDetailsError
          message={error instanceof Error ? error.message : 'An error occurred'}
          onRetry={() => refetch()}
        />
      );
    }

    if (!taskLog) {
      return (
        <LogDetailsError message="Task log not found" onBack={handleBack} />
      );
    }

    return (
      <div className="flex flex-1 flex-col gap-8 overflow-y-auto px-4 pb-20 sm:px-8">
        {/* Header */}
        <LogDetailsHeader
          taskTitle={formData.taskTitle}
          isEditing={isEditing}
          saving={updateTaskLogMutation.isPending}
          hasChanges={hasChanges}
          onBack={handleBack}
          onEdit={handleEdit}
          onSave={handleSave}
          onCancel={handleCancelEdit}
          onDelete={handleDelete}
        />
        <div className="max-w-full space-y-8">
          {/* Basic Information */}
          <TaskLogBasicInfo
            taskLog={formData}
            editForm={editForm}
            isEditing={isEditing}
            onInputChange={handleInputChange}
          />

          {/* Status and Priority */}
          <TaskLogStatusPriority
            taskLog={formData}
            editForm={editForm}
            isEditing={isEditing}
            onSelectChange={handleSelectChange}
          />

          {/* Additional Fields */}
          <TaskLogAdditionalFields
            taskLog={formData}
            editForm={editForm}
            isEditing={isEditing}
            onInputChange={handleInputChange}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="relative flex h-full flex-1 flex-col">
      {renderMainContent()}

      <DeleteTaskLogModal
        isOpen={showDeleteModal}
        isDeleting={deleteTaskLogMutation.isPending}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      <SuccessModal isOpen={showSuccessModal} />
    </div>
  );
};

export default TaskLogDetailsPage;
