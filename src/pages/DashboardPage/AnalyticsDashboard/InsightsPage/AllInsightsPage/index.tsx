import { AnimatePresence, motion } from 'framer-motion';
import { ChevronLeft, Search, X } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { insightsEmptyStateBg } from '@/assets/images';
import Pagination from '@/components/common/Pagination';
import { Button } from '@/components/ui/Button';
import DateRangePicker from '@/components/ui/DateRangePicker';
import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';
import { useDailyInsightsList } from '@/hooks/useDashboard';
import { usePagination } from '@/hooks/usePagination';
import { capitalizeAgentName } from '@/utils/agentUtils';
import { useAnalyticsParams } from '@/utils/urlParams';

import { InsightAccordion } from '../InsightAccordion';
import { InsightSkeleton } from '../InsightSkeleton';

const AllInsightsPage: React.FC = () => {
  const { page: currentPage, setPage: setCurrentPage } = usePagination();
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { tenantId } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const filterButtonRef = useRef<HTMLButtonElement>(null);
  const pageSize = 10;

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;
  const searchQuery = searchParams.get('search') || '';

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    createdBy: filters.agent || '',
    search: searchQuery,
    from: fromDate,
    to: toDate,
    page: currentPage - 1, // Convert to 0-based indexing
    pageSize: pageSize,
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.data?.insights || [];
  const totalCount = insightsResponse?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handleSearchChange = (value: string) => {
    const newSearchParams = new URLSearchParams(location.search);
    if (value) {
      newSearchParams.set('search', value);
    } else {
      newSearchParams.delete('search');
    }
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  const formatDateForBackend = (date: Date): string => {
    // Format as yyyy-MM-dd (LocalDate format)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleDateRangeApply = (range: { from?: Date; to?: Date }) => {
    const newSearchParams = new URLSearchParams(location.search);

    if (range.from) {
      newSearchParams.set('from', formatDateForBackend(range.from));
    } else {
      newSearchParams.delete('from');
    }

    if (range.to) {
      newSearchParams.set('to', formatDateForBackend(range.to));
    } else {
      newSearchParams.delete('to');
    }

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
    setShowDateRangePicker(false);
  };

  const getCurrentDateRange = () => {
    const from = fromDate ? new Date(fromDate) : undefined;
    const to = toDate ? new Date(toDate) : undefined;
    return { from, to };
  };

  const clearAllFilters = () => {
    const newSearchParams = new URLSearchParams();
    // Keep only suite and agent parameters
    const suite = searchParams.get('suite');
    const agent = searchParams.get('agent');

    if (suite) newSearchParams.set('suite', suite);
    if (agent) newSearchParams.set('agent', agent);

    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  const hasActiveFilters = () => {
    // Only show the clear component for actual filters (e.g., date range), not for plain search
    return !!(fromDate || toDate);
  };

  // The API already handles search filtering, so we don't need to filter again here
  const filteredInsights = insights;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className="flex h-full flex-col gap-6 p-6"
    >
      {/* Header */}
      <div className="relative flex items-center justify-between gap-4">
        <div className="flex items-start gap-2">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="h-5 w-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col items-start gap-1 text-blackTwo">
            <h1 className="text-lg font-semibold">
              Daily Insight Commentary (from{' '}
              {capitalizeAgentName(filters.agent || '')})
            </h1>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search insights..."
              value={searchQuery}
              onChange={e => handleSearchChange(e.target.value)}
              className="h-[44px] w-[215px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button
            ref={filterButtonRef}
            onClick={() => setShowDateRangePicker(true)}
            className="h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white"
          >
            <span>Filter</span>
            <Icons.DownloadCloud className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      <AnimatePresence>
        {hasActiveFilters() && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="flex items-center gap-3 rounded-lg border bg-white p-2"
          >
            <button
              onClick={clearAllFilters}
              className="flex h-[36px] items-center gap-2 rounded-full bg-[#4F4F4F] px-4 py-2 text-sm text-white transition-colors hover:bg-gray-900"
            >
              <span>Clear Filters</span>
              <X className="h-5 w-5" />
            </button>

            {(fromDate || toDate) && (
              <div className="flex items-center gap-2 rounded-lg bg-[#FFFAF7] p-2">
                <Icons.DataDate
                  className="h-5 w-5 text-black"
                  strokeWidth={1.5}
                />
                <div className="flex items-center gap-2">
                  <span className="flex h-[36px] items-center justify-center rounded-full border border-primary bg-white px-3 py-1 text-sm text-black">
                    {fromDate ? formatUserTimestamp(fromDate, 'date') : ''}
                  </span>
                  <span className="text-sm text-gray-500">-</span>
                  <span className="flex h-[36px] items-center justify-center rounded-full border border-primary bg-white px-3 py-1 text-sm text-black">
                    {toDate ? formatUserTimestamp(toDate, 'date') : ''}
                  </span>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Insights Content */}
      <div className="flex flex-1 flex-col gap-4">
        {isLoading ? (
          <InsightSkeleton count={5} />
        ) : isError ? (
          <div className="flex flex-col items-center justify-center py-12">
            <p className="mb-2 text-lg text-red-600">Error loading insights</p>
            <p className="text-sm text-gray-500">Please try again later</p>
          </div>
        ) : filteredInsights.length === 0 ? (
          <div
            className="relative flex h-[295px] w-full items-center justify-center overflow-hidden rounded-lg"
            style={{
              backgroundImage: `url(${insightsEmptyStateBg})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              paddingTop: '60px',
              paddingBottom: '60px',
            }}
          >
            {/* Dark overlay */}
            <div
              className="absolute inset-0"
              style={{
                backgroundColor: '#000000B2',
              }}
            />

            {/* Content */}
            <div className="relative z-10 flex flex-col items-center gap-6">
              {/* Icon with warning background */}
              <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-warning">
                <Icons.ChipExtractionRound className="h-8 w-8 text-white" />
              </div>

              {/* Text content */}
              <div className="flex flex-col items-center gap-3">
                <h3
                  className="text-center font-semibold text-white"
                  style={{
                    fontSize: '16px',
                    lineHeight: '100%',
                  }}
                >
                  No insights available today.
                </h3>
                <p
                  className="text-center font-normal text-white"
                  style={{
                    fontSize: '16px',
                    lineHeight: '24px',
                  }}
                >
                  As you activate your first agent tasks, this
                  <br />
                  space will come alive with real-time insights.
                </p>
              </div>
            </div>
          </div>
        ) : (
          filteredInsights.map((insight, index) => (
            <InsightAccordion
              key={insight.id}
              insight={insight}
              isLast={index === filteredInsights.length - 1}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {!isLoading && totalPages > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          onDownload={undefined}
          className="mt-auto"
          downloadButtonText="Download Report"
        />
      )}

      {/* Date Range Picker Popup */}
      <DateRangePicker
        isOpen={showDateRangePicker}
        onClose={() => setShowDateRangePicker(false)}
        onApply={handleDateRangeApply}
        initialRange={getCurrentDateRange()}
        anchorRef={filterButtonRef}
        dropdownPosition="bottom-right"
      />
    </motion.div>
  );
};

export default AllInsightsPage;
