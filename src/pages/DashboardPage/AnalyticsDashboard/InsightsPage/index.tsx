import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { useTenant } from '@/context/TenantContext';
import { useDailyInsightsList } from '@/hooks/useDashboard';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useAnalyticsParams } from '@/utils/urlParams';

import DailyInsightCommentary from './DailyInsightCommentary';

type InsightType = 'business' | 'technical';

const InsightsPage: React.FC = () => {
  const { tenantId } = useTenant();
  const location = useLocation();
  const navigate = useNavigate();
  const { filters } = useAnalyticsParams();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useOnClickOutside(dropdownRef, () => setIsDropdownOpen(false));

  // Get filters from URL params
  const searchParams = new URLSearchParams(location.search);
  const fromDate = searchParams.get('from') || undefined;
  const toDate = searchParams.get('to') || undefined;
  const searchQuery = searchParams.get('search') || undefined;
  const insightTypeParam = searchParams.get('type') as InsightType | null;
  const [insightType, setInsightType] = useState<InsightType>(
    insightTypeParam || 'business'
  );

  // Map insight type to source filter for API
  // Business insights: performance-metrics, assignment-state-review, outcome-based-mode
  // Technical insights: task-log-execution, task-log-analysis
  // Note: Backend should handle mapping 'business'/'technical' to actual source values
  const getSourceFilter = (type: InsightType): string | undefined => {
    return type; // Pass 'business' or 'technical' - backend will map to actual source values
  };

  // Build filter for insights - include agent filter by default
  const insightFilter = {
    tenantId: tenantId || '',
    createdBy: filters.agent || '',
    search: searchQuery,
    from: fromDate,
    to: toDate,
    page: 0,
    pageSize: 10,
    source: getSourceFilter(insightType), // Pass source filter to API
  };

  // Fetch insights using the hook
  const {
    data: insightsResponse,
    isLoading,
    isError,
  } = useDailyInsightsList(insightFilter, !!tenantId);

  const insights = insightsResponse?.data?.insights || [];

  const handleInsightTypeChange = (type: InsightType) => {
    setInsightType(type);
    setIsDropdownOpen(false);
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('type', type);
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  const insightTypeOptions: { value: InsightType; label: string }[] = [
    { value: 'business', label: 'Business Insights' },
    { value: 'technical', label: 'Technical Insights' },
  ];

  return (
    <div className="px-4 sm:px-6">
      {/* Header with Title and Filter Dropdown */}
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A1A1A]">
          {insightType === 'business'
            ? 'Business Insights'
            : 'Technical Insights'}
        </h2>

        {/* Insight Type Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex h-[48px] items-center gap-2 rounded-[10px] border border-gray-200 bg-white px-4 py-2 transition-colors hover:bg-gray-50"
          >
            <span className="text-sm font-medium text-[#1A1A1A]">
              {insightTypeOptions.find(opt => opt.value === insightType)?.label}
            </span>
            <motion.div
              animate={{ rotate: isDropdownOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </motion.div>
          </button>

          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute right-0 top-full z-50 mt-1 w-[200px] rounded-xl border border-gray-200 bg-white shadow-lg"
            >
              <div className="flex flex-col divide-y divide-gray-200">
                {insightTypeOptions.map(option => (
                  <button
                    key={option.value}
                    onClick={() => handleInsightTypeChange(option.value)}
                    className={`flex w-full items-center gap-2 p-3 text-left text-sm font-medium transition-colors first:rounded-t-xl last:rounded-b-xl hover:bg-gray-50 ${
                      insightType === option.value
                        ? 'bg-primary/10 text-primary'
                        : 'text-[#1A1A1A]'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      <DailyInsightCommentary
        insights={insights}
        isLoading={isLoading}
        isError={isError}
      />
    </div>
  );
};

export default InsightsPage;
